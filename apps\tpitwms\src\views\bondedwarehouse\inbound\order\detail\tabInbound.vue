<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  getInOrderInbound,
  getInOrderInboundItem,
} from '#/api/bondedwarehouse/inbound';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import batchGRModal from './BatchGRModal.vue';
import grCloseModal from './GRCloseModal.vue';
import unilateralGRModal from './UnilateralGRModal.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  hbl: {
    type: String,
    default: '',
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
});

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
const [GRCloseModal, GRCloseModalApi] = useVbenModal({
  connectedComponent: grCloseModal,
});
const [BatchGRModal, BatchGRModalApi] = useVbenModal({
  connectedComponent: batchGRModal,
});
const [UnilateralGRModal, UnilateralGRModalApi] = useVbenModal({
  connectedComponent: unilateralGRModal,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
function openGRCloseModal(id: string) {
  GRCloseModalApi.setData({ id });
  GRCloseModalApi.open();
}
function openBatchGRModal(id: string) {
  BatchGRModalApi.setData({ id });
  BatchGRModalApi.open();
}
function openUnilateralGRModal(id: string) {
  UnilateralGRModalApi.setData({ id });
  UnilateralGRModalApi.open();
}
const itemGridOptions = reactive<VxeGridProps>({
  cellStyle({ row, column }) {
    if (['ActualQty', 'MissingQty'].includes(column.field) && row.ActualQty) {
      if (row.ActualQty > row.DeliveredQuantity) {
        return {
          backgroundColor: '#ffe3e3',
        };
      } else if (row.ActualQty < row.DeliveredQuantity) {
        return {
          backgroundColor: '#fff9db',
        };
      }
    }
    if (
      ['ActualBatch'].includes(column.field) &&
      row.ActualBatch &&
      row.BatchNumber !== row.ActualBatch
    ) {
      return {
        backgroundColor: '#fff9db',
      };
    }
    return null;
  },
  columns: [
    /* { type: 'seq', width: 48 }, */
    { field: 'ItemNumber', title: 'Inbound项号', width: 108, treeNode: true },
    { field: 'POItemNumber', title: '采购订单项号', width: 108 },
    { field: 'Plant', title: '工厂编码', width: 80 },
    { field: 'StorageLocation', title: '库存地点', width: 80 },
    { field: 'SSCCCount', title: '托盘个数', width: 100 },
    {
      field: 'MaterialCode',
      title: '产品编号',
      width: 108,
      slots: { default: 'skucode' },
    },
    {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 72,
      align: 'center',
      slots: { default: 'batchmanage' },
    },
    {
      field: 'BatchNumber',
      title: '产品批次',
      width: 100,
      slots: { default: 'batch' },
    },
    {
      field: 'ActualBatch',
      title: '实收批次',
      minWidth: 136,
      /* formatter({ cellValue }) {
        return [...new Set(cellValue.split(','))].join(',');
      }, */
      /* slots: { default: 'actualbatch' }, */
    },
    { field: 'DeliveredQuantity', title: '产品数量', minWidth: 128 },
    { field: 'ActualQty', title: '实收数量', minWidth: 100 },
    { field: 'MissingQty', title: '少货数量', minWidth: 100 },
    { field: 'QtyIsGR', title: '已GR数量', minWidth: 100 },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  rowStyle({ row }) {
    if (row.Qty === 0) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
  size: 'mini',
  toolbarConfig: {
    enabled: false,
  },
  treeConfig: {
    transform: true,
    rowField: 'ItemNumber',
    parentField: 'ParentItem',
    showLine: true,
    /* expandAll: true, */
  },
});
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});

const gridOptions = reactive<VxeGridProps>({
  id: 'BWInOrderInboundGR',
  /* checkboxConfig: { highlight: true, range: true }, */
  columns: [
    /* { type: 'checkbox', width: 48, fixed: 'left' }, */
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    {
      field: 'DeliveryNumber',
      title: 'Inbound',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'LorealDocument',
      title: '采购订单',
      minWidth: 96,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'DeliveryDate',
      title: '计划送达',
      minWidth: 88,
      formatter: 'formatDate',
    },
    {
      field: 'TotalQty',
      title: '总件数',
      minWidth: 88,
    },
    {
      field: 'ActualQty',
      title: '实收数量',
      minWidth: 88,
    },
    {
      field: 'FirstReceivedDate',
      title: '首次收货时间',
      minWidth: 136,
      formatter: 'formatDateTime',
    },
    {
      field: 'LastReceivedDate',
      title: '末次收货时间',
      minWidth: 136,
      formatter: 'formatDateTime',
    },
    {
      field: 'LastGRDate',
      title: '最近GR日期',
      minWidth: 136,
      formatter: 'formatDateTime',
    },
    {
      field: 'IsGRClosed',
      title: '单据状态',
      minWidth: 88,
      filters: [
        { label: '未关闭', value: false },
        { label: '已关闭', value: true },
      ],
      filterMultiple: false,
      slots: { default: 'isGRClosed' },
    },
    {
      field: 'Options',
      title: 'GR操作',
      slots: { default: 'action' },
      width: 240,
      align: 'center',
    },
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: false,
  },
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getInOrderInbound(props.id);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const gridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    await handleItemData([]);
    if (expanded) {
      handleItemLoading(true);
      const itemData = await getInOrderInboundItem(row.Guid);
      await handleHeadRow(row);
      await handleItemData(itemData);
      handleItemLoading(false);
      itemGridApi.grid?.setAllTreeExpand(true);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function handleHeadRow(row: any) {
  gridApi.grid.setCurrentRow(row);
}
function handleItemData(data: any) {
  itemGridApi.setGridOptions({
    data,
  });
}
function handleItemLoading(isLoading: boolean) {
  itemGridApi.setLoading(isLoading);
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">关联Inbound</span>
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #isGRClosed="{ row }">
        <Tag :color="row.IsGRClosed ? 'green' : 'blue'">
          {{ row.IsGRClosed ? '已关闭' : '未关闭' }}
        </Tag>
      </template>
      <template #action="{ row }">
        <a-button
          :disabled="row.IsGRClosed"
          class="mr-2"
          size="small"
          @click="openBatchGRModal(row.Guid)"
        >
          分批GR
        </a-button>
        <a-button
          :disabled="row.IsGRClosed"
          class="mr-2"
          size="small"
          type="primary"
          @click="openGRCloseModal(row.Guid)"
        >
          关闭GR
        </a-button>
        <a-button
          :disabled="row.IsGRClosed"
          class="single-inbound-btn mr-2"
          size="small"
          @click="openUnilateralGRModal(row.Guid)"
        >
          单边入库
        </a-button>
      </template>
      <template #expand_content>
        <div>
          <ItemGrid>
            <template #skucode="{ row }">
              <a-button
                size="small"
                type="link"
                @click="openSkuDrawer(row.MaterialCode)"
              >
                {{ row.MaterialCode }}
              </a-button>
            </template>
            <template #batch="{ row }">
              <a-button
                v-if="row.BatchNumber"
                size="small"
                type="link"
                @click="openBatchDrawer(row.MaterialCode, row.BatchNumber)"
              >
                {{ row.BatchNumber }}
              </a-button>
            </template>
            <!-- <template #actualbatch="{ row }">
              <a
                v-for="item in row.ActualBatch.split(',')"
                :key="item"
                :value="item"
                @click="openBatchDrawer(row.MaterialCode, item)"
              >
                {{ item }}
              </a>
            </template> -->
            <template #batchmanage="{ row }">
              <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
                {{ row.isBatchManagementRequired ? '是' : '否' }}
              </Tag>
            </template>
            <template #batchstatus="{ row }">
              <Tag :color="row.BatchStatus ? 'red' : 'green'">
                {{ row.BatchStatus ? '是' : '否' }}
              </Tag>
            </template>
          </ItemGrid>
        </div>
      </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
    <GRCloseModal @success="handleSuccess()" />
    <BatchGRModal @success="handleSuccess()" />
    <UnilateralGRModal @success="handleSuccess()" />
  </div>
</template>

<style scoped>
.single-inbound-btn {
  color: white !important;
  background-color: #eab308 !important;
  border-color: #eab308 !important;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%);
  transition: background-color 0.2s ease-in-out;
}

.single-inbound-btn:hover:not(:disabled) {
  background-color: rgb(234 179 8 / 75%) !important;
  border-color: rgb(234 179 8 / 75%) !important;
}

.single-inbound-btn:disabled {
  color: rgb(0 0 0 / 25%) !important;
  cursor: not-allowed !important;
  background-color: rgb(0 0 0 / 4%) !important;
  border-color: #d9d9d9 !important;
  opacity: 1 !important;
}
</style>
