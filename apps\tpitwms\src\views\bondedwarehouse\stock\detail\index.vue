<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { onMounted, reactive, ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';
import { downloadFileFromBlob } from '@vben/utils';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getBWStockDetailList } from '#/api/bondedwarehouse/stock';
import { addExportRecord, exportFile, getOptions } from '#/api/common';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
const searchField: any = reactive({
  DocNo: undefined,
  MatCode: undefined,
});
const loadingRef = ref(false);
const gridQueryParams = ref({}) as any;
const stock: any = reactive({
  StockQty: 0,
  BlockedQty: 0,
  UnrestrictedQty: 0,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockDetail',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'StockStatus',
      title: '库存状态',
      width: 88,
      filters: [],
    },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 96,
      filters: [],
    },
    {
      field: 'DocNo',
      title: '当前业务单据',
      width: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'HBL',
      title: '入库提单HBL',
      width: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'BLNo',
      title: '出库提单BL',
      width: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'PalletNo',
      title: '托盘号',
      width: 96,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'material' },
    },
    {
      field: 'MaterialType',
      title: '物料类型',
      width: 88,
      filters: [],
    },
    {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 88,
      slots: { default: 'batchmanage' },
      /* formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      }, */
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
    },
    {
      field: 'BatchNo',
      title: '实物批号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'batch' },
    },
    {
      field: 'ActualExpiration',
      title: '实物效期',
      width: 88,
      formatter: 'formatDate',
    },
    {
      field: 'SAPBatch',
      title: 'SAP批次',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'BatchStatus',
      title: '批次状态',
      width: 88,
      slots: { default: 'batchstatus' },
      filters: [
        { label: '正常', value: 0 },
        { label: '限制', value: 1 },
        { label: '不存在', value: -1 },
      ],
    },
    {
      field: 'ManufactureDate',
      title: '制造日期',
      width: 96,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
      visible: false,
    },
    {
      field: 'ShelfLifeExpirationDate',
      title: '批次效期',
      width: 96,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
      visible: false,
    },
    {
      field: 'lifespanInDays',
      title: '产品期限',
      width: 80,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
      visible: false,
    },
    {
      field: 'StockQty',
      title: '库存数量',
      width: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'UnrestrictedQty',
      title: '非限制库存',
      width: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'BlockedQty',
      title: '冻结库存',
      width: 88,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'IsWorking',
      title: '正操作',
      width: 80,
      align: 'center',
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'isworking' },
    },
    {
      field: 'LockedByMovement',
      title: '调整锁',
      width: 80,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'lockbymovement' },
    },
    {
      field: 'LockedByTaking',
      title: '盘点锁',
      width: 80,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'lockbytaking' },
    },
    {
      field: 'LockedByAPI',
      title: 'API锁',
      width: 80,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'lockbyapi' },
    },
    {
      field: 'Division',
      title: 'Division',
      width: 100,
      filters: [],
      visible: false,
    },
    {
      field: 'BrandName',
      title: '品牌',
      width: 100,
      filters: [],
    },
    {
      field: 'OriginName',
      title: '原产国',
      width: 100,
      filters: [],
      visible: false,
    },
    {
      field: 'OwnerShortName',
      title: '货主',
      width: 100,
      filters: [],
    },
    {
      field: 'WarehouseName',
      title: '仓库',
      width: 88,
      filters: [],
    },
    {
      field: 'AreaName',
      title: '库区',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'BIN',
      title: '货位',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'SSCC',
      title: 'SSCC',
      width: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'CName',
      title: '中文品名',
      minWidth: 200,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    /* {
      field: 'CreateDate',
      title: '创建日期',
      width: 150,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    }, */
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        gridQueryParams.value = queryParams;
        return getBWStockDetailList(queryParams)
          .then((res) => {
            Object.assign(stock, res.stock);
            return res;
          })
          .catch(() => {})
          .finally(() => {});
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  footerRowStyle: { 'font-weight': 'bold ', backgroundColor: '#ebfbee' },
  footerMethod({ columns }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (
          ['BlockedQty', 'StockQty', 'UnrestrictedQty'].includes(column.field)
        ) {
          return stock[column.field];
        }

        return null;
      }),
    ];
  },
  cellStyle({ row, column }) {
    if (
      ['ActualExpiration'].includes(column.field) &&
      row.ActualExpiration &&
      row.ActualExpiration !== row.ShelfLifeExpirationDate
    ) {
      return {
        backgroundColor: '#fff9db',
      };
    }
    return null;
  },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出xlsx',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
function handleExport() {
  addExportRecord({
    typ: 'StockDetail',
    params: gridQueryParams.value,
    columns: gridApi.grid
      .getColumns()
      .filter((item) => item.field && item.title)
      .map((item) => ({
        field: item.field,
        title: item.title,
      })),
  })
    .then((res) => {
      loadingRef.value = true;
      exportFile(res)
        .then((res) => {
          downloadFileFromBlob({
            source: res.data,
            fileName: `库存明细_${Date.now()}.xlsx`,
          });
        })
        .catch(() => {})
        .finally(() => {
          loadingRef.value = false;
        });
    })
    .catch(() => {})
    .finally(() => {});
}
async function fetch() {
  const options = await getOptions('StockDetail');
  const fieldList = [
    'StockStatus',
    'StorageLocation',
    'MaterialType',
    'BrandName',
    'OwnerShortName',
    'WarehouseName',
    'Division',
    'OriginName',
  ];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height v-loading="loadingRef">
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.MatCode"
            allow-clear
            class="mr-2 w-60"
            placeholder="物料编号"
            @search="searchEvent('MatCode')"
          />
          <a-input-search
            v-model:value="searchField.DocNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="业务单据"
            @search="searchEvent('DocNo')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #material="{ row }">
        <a class="text-blue-500" @click="openSkuDrawer(row.MatCode)">
          {{ row.MatCode }}
        </a>
      </template>
      <template #batch="{ row }">
        <a
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
      <template #batchmanage="{ row }">
        <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
          {{ row.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </template>
      <template #batchstatus="{ row }">
        <Tag
          v-if="row.BatchStatus !== null"
          :color="
            row.BatchStatus === 0
              ? 'green'
              : row.BatchStatus === 1
                ? 'orange'
                : 'red'
          "
        >
          {{
            row.BatchStatus === 0
              ? '正常'
              : row.BatchStatus === 1
                ? '限制'
                : '不存在'
          }}
        </Tag>
      </template>
      <template #isworking="{ row }">
        <Tag :color="row.IsWorking ? 'blue' : 'green'">
          {{ row.IsWorking ? '是' : '否' }}
        </Tag>
      </template>
      <template #lockbymovement="{ row }">
        <Tag :color="row.LockedByMovement ? 'blue' : 'green'">
          {{ row.LockedByMovement ? '是' : '否' }}
        </Tag>
      </template>
      <template #lockbytaking="{ row }">
        <Tag :color="row.LockedByTaking ? 'blue' : 'green'">
          {{ row.LockedByTaking ? '是' : '否' }}
        </Tag>
      </template>
      <template #lockbyapi="{ row }">
        <Tag :color="row.LockedByAPI ? 'blue' : 'green'">
          {{ row.LockedByAPI ? '是' : '否' }}
        </Tag>
      </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
  </Page>
</template>
