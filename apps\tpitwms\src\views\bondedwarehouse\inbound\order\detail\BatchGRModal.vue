<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getInboundBatchReceivingItem } from '#/api/bondedwarehouse/inbound';

import batchGRConfirmModal from './BatchGRConfirmModal.vue';

const emit = defineEmits(['success']);

const data = ref();
const [BatchGRConfirmModal, BatchGRConfirmModalApi] = useVbenModal({
  connectedComponent: batchGRConfirmModal,
});

const gridOptions = reactive<VxeGridProps>({
  id: 'BatchGRSelection',
  checkboxConfig: {
    highlight: true,
    range: true,
    checkMethod: ({ row }: any) => {
      return !(row.ReceivedMasterBatchStatus === -1);
    },
  },
  currentRowConfig: {
    beforeSelectMethod({ row }) {
      if (row.ReceivedMasterBatchStatus === -1) {
        return false;
      }
      return true;
    },
  },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    { type: 'seq', title: '#', width: 60 },
    {
      field: 'InboundItem',
      title: 'Item',
      width: 72,
    },
    {
      field: 'MatCode',
      title: '产品编号',
      width: 88,
    },
    /* {
      field: 'MatName',
      title: '产品名称',
      minWidth: 200,
    }, */
    {
      field: 'InboundBatch',
      title: '产品批次',
      width: 88,
    },
    {
      field: 'ReceivedBatch',
      title: '实收批次',
      width: 88,
    },
    {
      field: 'ReceivedMasterBatchStatus',
      title: '实收批次状态',
      width: 100,
      slots: { default: 'batchstatus' },
      filters: [
        { label: '正常', value: 0 },
        { label: '限制', value: 1 },
        { label: '不存在', value: -1 },
      ],
    },
    {
      field: 'EstimatedReceivedQty',
      title: '产品数量',
      width: 88,
    },
    {
      field: 'ReceivedQty',
      title: '实收数量',
      width: 88,
    },
    {
      field: 'PalletNo',
      title: '入库托盘号',
      width: 80,
    },
    {
      field: 'SSCC',
      title: 'SSCC',
      minWidth: 144,
    },
  ],
  height: 400,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getInboundBatchReceivingItem(data.value.id);
      },
    },
    seq: true,
  },
  size: 'mini',
  toolbarConfig: {
    enabled: false,
  },
  rowStyle({ row }) {
    if (row.ReceivedMasterBatchStatus === -1) {
      return {
        backgroundColor: '#ffc9c9',
        color: '#222',
      };
    }
  },
});

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-[1000px]',
  draggable: true,
  fullscreenButton: false,
  closeOnClickModal: false,
  confirmText: '提交检测',
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await handleConfirm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      /* gridApi.grid.commitProxy('query'); */
    }
  },
  title: '请选择已收货未GR产品',
});

async function handleConfirm() {
  const checkedRecords = gridApi.grid.getCheckboxRecords();
  if (checkedRecords.length === 0) {
    message.warning('请选择要进行GR的产品');
    return;
  }

  const selectedIds = checkedRecords.map((item: any) => item.Guid);

  // 打开确认弹窗
  BatchGRConfirmModalApi.setData({
    id: data.value.id,
    ids: selectedIds,
    selectedItems: checkedRecords,
  });
  BatchGRConfirmModalApi.open();
  /* modalApi.close(); */
}

function handleSuccess() {
  modalApi.close();
  emit('success');
}
</script>

<template>
  <BaseModal>
    <div class="mb-4">
      <div class="ml-2 text-sm text-gray-600">
        实收批次主数据不存在的产品无法勾选，请反馈并等待主数据维护！
      </div>
    </div>
    <Grid>
      <template #batchstatus="{ row }">
        <Tag
          v-if="row.ReceivedMasterBatchStatus !== null"
          :color="
            row.ReceivedMasterBatchStatus === 0
              ? 'green'
              : row.ReceivedMasterBatchStatus === 1
                ? 'orange'
                : 'red'
          "
        >
          {{
            row.ReceivedMasterBatchStatus === 0
              ? '正常'
              : row.ReceivedMasterBatchStatus === 1
                ? '限制'
                : '不存在'
          }}
        </Tag>
      </template>
    </Grid>
    <BatchGRConfirmModal @success="handleSuccess()" />
  </BaseModal>
</template>
