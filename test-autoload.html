<!DOCTYPE html>
<html>
<head>
    <title>VxeGrid AutoLoad Test</title>
</head>
<body>
    <h1>VxeGrid AutoLoad Test</h1>
    <p>请打开浏览器开发者工具查看控制台输出，验证自动加载是否正常工作。</p>
    
    <h2>测试说明</h2>
    <ul>
        <li>修复前：用户设置的 autoLoad: true 会被全局配置覆盖</li>
        <li>修复后：用户配置应该优先于全局配置</li>
        <li>查看控制台是否有 "VxeGrid: Triggering auto load" 日志</li>
    </ul>
    
    <h2>修复内容</h2>
    <pre>
// 修复前的合并顺序（错误）：
const defaultGridOptions = mergeWithArrayOverride(
  {},
  toRaw(gridOptions.value),  // 用户配置
  toRaw(globalGridConfig),   // 全局配置会覆盖用户配置
);

// 修复后的合并顺序（正确）：
const defaultGridOptions = mergeWithArrayOverride(
  {},
  toRaw(globalGridConfig),   // 全局配置作为基础
  toRaw(gridOptions.value),  // 用户配置覆盖全局配置
);
    </pre>
</body>
</html>
