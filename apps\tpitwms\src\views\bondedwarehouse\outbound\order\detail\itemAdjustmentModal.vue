<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { /* useVbenDrawer, */ useVbenModal } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Col, InputNumber, message, Row, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutOrderItemAdd,
  bwOutOrderItemRemove,
  bwOutOrderItemUpdateQty,
  getOutOrderItem,
  getUnusedStock,
} from '#/api/bondedwarehouse/outbound';
/* import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue'; */

const emit = defineEmits(['success']);

/* const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
} */
const data = ref();
const searchField: any = reactive({
  MatCode: undefined,
  BatchNo: undefined,
});
const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  fullscreenButton: true,
  closeOnClickModal: false,
  destroyOnClose: true,
  zIndex: 900,
  /* showConfirmButton: false, */
  footer: false,
  /* onCancel() {
    modalApi.close();
  }, */
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    } else {
      emit('success');
    }
  },
  title: '出库订单明细调整',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'UnusedStockList',
  checkboxConfig: {
    highlight: true,
    range: true,
    checkMethod: ({ row }: any) => {
      return !(
        row.LockedByMovement ||
        row.LockedByTaking ||
        row.LockedByAPI ||
        row.UnrestrictedQty === 0
      );
    },
  },
  columns: [
    { type: 'checkbox', width: 40 },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 96,
    },
    {
      field: 'HBL',
      title: '入库提单',
      minWidth: 160,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    { field: 'Invoice', title: '发票号', width: 108 },
    {
      field: 'PalletNo',
      title: '入库托盘',
      minWidth: 100,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'MaterialType',
      title: '物料类型',
      width: 88,
      filters: [],
    },
    {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 88,
      align: 'center',
      slots: { default: 'batchmanage' },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
    },
    {
      field: 'BatchNo',
      title: '批次',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'BatchStatus',
      title: '批次状态',
      width: 88,
      slots: { default: 'batchstatus' },
      filters: [
        { label: '正常', value: 0 },
        { label: '限制', value: 1 },
        { label: '不存在', value: -1 },
      ],
    },
    {
      field: 'ActualExpiration',
      title: '实物效期',
      width: 88,
      formatter: 'formatDate',
    },
    {
      field: 'ReceivedDate',
      title: '收货日期',
      width: 136,
      formatter: 'formatDateTime',
    },
    { field: 'StockQty', title: '库存数量', width: 100 },
    { field: 'UnrestrictedQty', title: '非限制库存', width: 100 },
    { field: 'BlockedQty', title: '冻结库存', width: 96 },
    {
      field: 'LockedByMovement',
      title: '调整锁',
      width: 80,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'lockbymovement' },
      /* formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      }, */
    },
    {
      field: 'LockedByTaking',
      title: '盘点锁',
      width: 80,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'lockbytaking' },
    },
    {
      field: 'LockedByAPI',
      title: 'API锁',
      width: 80,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'lockbyapi' },
    },
    {
      field: 'BrandName',
      title: '品牌',
      width: 100,
      filters: [],
    },
    { field: 'AreaName', title: '库区', width: 100 },
    { field: 'BIN', title: '货位', width: 100 },
    {
      field: 'CName',
      title: '中文品名',
      minWidth: 240,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
  ],
  filterConfig: {
    remote: false,
  },
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async (/* { $grid } */) => {
        const res = await getUnusedStock(data.value.id);
        /* allBrands.value = res.allBrands;
        const iBrandColumn = $grid.getColumnByField('iBrandName');
        if (iBrandColumn) {
          $grid.setFilter(iBrandColumn, allBrands.value);
        } */
        return res.data;
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const itemGridOptions = reactive<VxeGridProps>({
  id: 'OrderItemStockList',
  checkboxConfig: {
    highlight: true,
    range: true,
    /* checkMethod: ({ row }) => {
      return !(row.LockedByMovement || row.LockedByTaking);
    }, */
  },
  columns: [
    { type: 'checkbox', width: 40 },
    { field: 'Invoice', title: '发票号', width: 108 },
    {
      field: 'PalletNo',
      title: '入库托盘',
      width: 100,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'SSCC',
      title: 'SSCC',
      minWidth: 144,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'oMatType',
      title: '物料类型',
      width: 88,
      filters: [],
    },
    {
      field: 'BatchNo',
      title: '批次',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'ActualExpiration',
      title: '实物效期',
      width: 88,
      formatter: 'formatDate',
    },
    {
      field: 'GoodsNum',
      title: '本次出库数量',
      width: 120,
      editRender: { name: 'AInput', autofocus: '' },
      slots: { edit: 'goodsnum_edit' },
    },
    { field: 'Residual', title: '剩余可用库存', width: 120 },
    { field: 'RetentionNum', title: '建议留样数量', width: 100 },
    {
      field: 'AreaName',
      title: '来源库区',
      width: 88,
    },
    {
      field: 'BIN',
      title: '来源货位',
      width: 96,
    },
    {
      field: 'oDivision',
      title: 'Division',
      width: 80,
    },
    {
      field: 'oBrandName',
      title: '品牌',
      width: 80,
    },
    {
      field: 'oOriginName',
      title: '原产国',
      width: 64,
    },
    {
      field: 'oBarCode',
      title: '条码',
      width: 128,
    },
    {
      field: 'oCName',
      title: '中文品名',
      minWidth: 220,
    },
  ],
  editConfig: {
    trigger: 'dblclick',
    mode: 'row',
    showIcon: true,
    showStatus: true,
  },
  filterConfig: {
    remote: false,
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async (/* { $grid } */) => {
        const res = await getOutOrderItem(data.value.id);
        /* allBrands.value = res.allBrands;
        const iBrandColumn = $grid.getColumnByField('iBrandName');
        if (iBrandColumn) {
          $grid.setFilter(iBrandColumn, allBrands.value);
        } */
        return res.data;
      },
    },
    seq: true,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const itemGridEvents: VxeGridListeners = {
  editClosed({ row, rowIndex }) {
    handleUpdateByRow(row, rowIndex);
  },
};
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
  gridEvents: itemGridEvents,
});

function handleUpdateByRow(row: any, rowIndex: number) {
  if (itemGridApi.grid.isUpdateByRow(row)) {
    itemGridApi.setLoading(true);
    bwOutOrderItemUpdateQty(row.Guid, row.GoodsNum)
      .then(() => {
        message.success(`序号${rowIndex + 1} 保存成功！ `);
        gridApi.grid.commitProxy('query');
        itemGridApi.grid.commitProxy('query');
      })
      .catch(() => {
        itemGridApi.grid.revertData(row);
      })
      .finally(() => {
        itemGridApi.setLoading(false);
      });
  }
}
/* function handleClose() {
  modalApi.close();
  emit('success');
} */
function searchEvent(field: string, type: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }
    if (searchField[field]) {
      option.data = searchField[field];
      option.value = option.data;
      option.checked = true;
    } else {
      if (type) {
        option.data = undefined;
        option.checked = false;
      }
    }
    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}

function handleAdd() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要添加的库存明细`);
    return;
  }
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwOutOrderItemAdd(data.value.id, checkedRecords)
    .then(() => {
      /* modalApi.close(); */
      gridApi.grid.commitProxy('query');
      itemGridApi.grid.commitProxy('query');
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
function handleRemove() {
  const checkedRecords = itemGridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要添加的库存明细`);
    return;
  }
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwOutOrderItemRemove(data.value.id, checkedRecords)
    .then(() => {
      gridApi.grid.commitProxy('query');
      itemGridApi.grid.commitProxy('query');
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
async function fetch() {}
</script>
<template>
  <Modal>
    <Row :gutter="12" class="h-1/2">
      <Col :span="24" class="h-full">
        <Grid>
          <template #toolbar_left>
            <a-button class="mr-1" @click="clearFilterAndSort()">
              <AntClear class="size-5" />
            </a-button>
            <div class="hidden pl-1 md:block">
              <a-input-search
                v-model:value="searchField.MatCode"
                allow-clear
                class="mr-2 w-44"
                placeholder="物料编号"
                @search="searchEvent('MatCode', 'Input')"
              />
              <a-input-search
                v-model:value="searchField.BatchNo"
                allow-clear
                class="mr-2 w-44"
                placeholder="批号"
                @search="searchEvent('BatchNo', 'Input')"
              />
            </div>
          </template>
          <template #toolbar-tools>
            <a-button type="primary" @click="handleAdd()">添加明细</a-button>
          </template>
          <template #batchmanage="{ row }">
            <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
              {{ row.isBatchManagementRequired ? '是' : '否' }}
            </Tag>
          </template>
          <template #batchstatus="{ row }">
            <Tag
              :color="
                row.BatchStatus === 0
                  ? 'green'
                  : row.BatchStatus === 1
                    ? 'orange'
                    : 'red'
              "
            >
              {{
                row.BatchStatus === 0
                  ? '正常'
                  : row.BatchStatus === 1
                    ? '限制'
                    : '不存在'
              }}
            </Tag>
          </template>
          <template #lockbymovement="{ row }">
            <Tag :color="row.LockedByMovement ? 'blue' : 'green'">
              {{ row.LockedByMovement ? '是' : '否' }}
            </Tag>
          </template>
          <template #lockbytaking="{ row }">
            <Tag :color="row.LockedByTaking ? 'blue' : 'green'">
              {{ row.LockedByTaking ? '是' : '否' }}
            </Tag>
          </template>
          <template #lockbyapi="{ row }">
            <Tag :color="row.LockedByAPI ? 'blue' : 'green'">
              {{ row.LockedByAPI ? '是' : '否' }}
            </Tag>
          </template>
        </Grid>
      </Col>
    </Row>
    <Row :gutter="12" class="h-1/2">
      <Col :span="24" class="h-full">
        <ItemGrid>
          <template #toolbar_left>
            <div class="hidden pl-1 text-base font-medium md:block">
              <span class="mr-2">出库订单明细</span>
            </div>
          </template>
          <template #toolbar-tools>
            <a-button type="primary" @click="handleRemove()">移除明细</a-button>
          </template>
          <template #goodsnum_edit="{ row }">
            <InputNumber
              v-model:value="row.GoodsNum"
              :disabled="row.OrderPickUpStatus"
              :min="1"
            />
          </template>
        </ItemGrid>
      </Col>
    </Row>
    <!-- <SkuDrawer />
    <BatchDrawer /> -->
  </Modal>
</template>
