import { requestClient, sleep } from '#/api/request';

/**
 * 出库作业 出库订单相关API
 */
export async function getBWOutOrderList(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/order/getList', {
    params: obj,
  });
}

export async function bwOutOrderCreate(params: object) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/order/create', params);
}

export async function bwOutOrderDelete(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/order/del', { id });
}

export async function getOutOrderHead(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/order/head/getData', {
    params: { id },
  });
}

export async function bwOutOrderHeadUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/order/head/update',
    params,
  );
}

export async function getOutOrderItem(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/order/item/getList', {
    params: { id },
  });
}

export async function getUnusedStock(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/outbound/order/unusedStock/getList',
    {
      params: { id },
    },
  );
}

export async function bwOutOrderItemAdd(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/order/item/add', {
    id,
    ids,
  });
}

export async function bwOutOrderItemRemove(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/order/item/remove', {
    id,
    ids,
  });
}

export async function bwOutOrderItemUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/order/item/update',
    params,
  );
}

export async function bwOutOrderItemUpdateQty(id: string, value: number) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/order/item/update/qty', {
    id,
    value,
  });
}

export async function bwOutOrderItemStatusUpdate(
  id: string,
  ids: Array<string>,
  typ: number,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/order/item/status/update',
    { id, ids, typ },
  );
}

export async function bwOutOrderItemProcessingRequiredUpdate(
  ids: Array<string>,
  typ: number,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/order/item/processingRequired/update',
    { ids, typ },
  );
}

export async function getOutProcessingItem(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/outbound/processing/item/getList',
    {
      params: { id },
    },
  );
}

export async function bwOutProcessingItemUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/processing/item/update',
    params,
  );
}

export async function bwOutProcessingItemMultiUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/processing/item/multiUpdate',
    params,
  );
}

export async function bwOutProcessingItemStatusUpdate(
  id: string,
  ids: Array<string>,
  typ: number,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/processing/item/status/update',
    { id, ids, typ },
  );
}

export async function getOutTallyItem(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/tally/item/getList', {
    params: { id },
  });
}

export async function bwOutTallyItemGetData(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/tally/item/getData', {
    params: { id },
  });
}

export async function bwOutTallyItemConfirm(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/tally/item/confirm',
    params,
  );
}

export async function bwOutOrderCheckIsProcessingRequired(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/outbound/order/check/isProcessingRequired',
    {
      params: { id },
    },
  );
}

export async function bwOutOrderLISLabelWorkCreate(id: string) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/order/lis/lablework/create',
    { id },
  );
}

export async function bwOutTallyItemCancel(ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/tally/item/cancel', {
    ids,
  });
}
