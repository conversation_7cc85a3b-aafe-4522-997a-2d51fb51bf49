<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import {
  defineProps,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
} from 'vue';

import { useVbenDrawer, useVbenForm, useVbenModal } from '@vben/common-ui';
import { AntClear, AntSandBox } from '@vben/icons';

import {
  DatePicker,
  Dropdown,
  Menu,
  MenuItem,
  message,
  Modal,
  /* CheckableTag, */ Popconfirm,
  Tag,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwInOrderItemInvoiceUpdate,
  bwInOrderItemStatusUpdate,
  bwInOrderItemUpdate,
  getInOrderItem,
} from '#/api/bondedwarehouse/inbound';
import materialDrawer from '#/views/maintenance/data/material/materialDrawer.vue';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import inboundImportModal from './InboundImportModal.vue';
import inboundRemoveDrawer from './InboundRemoveDrawer.vue';
import receivingModal from './ReceivingModal.vue';
import splitFormModal from './splitFormModal.vue';
import splitModal from './SplitModal.vue';
import stockSetup from './StockSetup.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  hbl: {
    type: String,
    default: '',
  },
  inStatus: {
    type: Boolean,
    default: false,
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
  owner: {
    type: String,
    default: '',
  },
});

const allBrands = ref([]) as any;
const allDivisions = ref([]) as any;
/* const filterBrand = ref('all'); */

const [InboundImportModal, InboundImportModalApi] = useVbenModal({
  connectedComponent: inboundImportModal,
});
const [InboundRemoveDrawer, InboundRemoveDrawerApi] = useVbenDrawer({
  connectedComponent: inboundRemoveDrawer,
});
const [ReceivingModal, ReceivingModalApi] = useVbenModal({
  connectedComponent: receivingModal,
});
const [SplitModal, SplitModalApi] = useVbenModal({
  connectedComponent: splitModal,
});
const [SplitFormModal, SplitFormModalApi] = useVbenModal({
  connectedComponent: splitFormModal,
});
const [StockSetupModal, stockSetupModalApi] = useVbenModal({
  connectedComponent: stockSetup,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
const [MaterialDrawer, materialDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: materialDrawer,
});
function openInboundImportModal() {
  InboundImportModalApi.setData(props);
  InboundImportModalApi.open();
}
function openInboundRemoveDrawer() {
  InboundRemoveDrawerApi.setData(props);
  InboundRemoveDrawerApi.open();
}
function openReceivingModal(id: string) {
  ReceivingModalApi.setData({ id });
  ReceivingModalApi.open();
}
function openSplitModal() {
  SplitModalApi.setData({ id: props.id });
  SplitModalApi.open();
}
function openSplitFormModal(rowData: any) {
  SplitFormModalApi.setData(rowData);
  SplitFormModalApi.open();
}
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openMaterialDrawer(id: string) {
  materialDrawerApi.setData({ id });
  materialDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
const gridOptions = reactive<VxeGridProps>({
  id: 'BWInOrderItem',
  checkboxConfig: { highlight: true, range: true },
  cellStyle({ row, column }) {
    if (
      ['MissingQty', 'ReceivedQty'].includes(column.field) &&
      row.ReceivedQty !== null
    ) {
      if (row.ReceivedQty > row.EstimatedReceivedQty) {
        return {
          backgroundColor: '#ffe3e3',
        };
      } else if (
        row.ReceivingStatus &&
        row.ReceivedQty < row.EstimatedReceivedQty
      ) {
        return {
          backgroundColor: '#fff9db',
        };
      }
    }
    if (
      ['ReceivedBatch'].includes(column.field) &&
      row.ReceivedBatch &&
      row.InboundBatch !== row.ReceivedBatch
    ) {
      return {
        backgroundColor: '#fff9db',
      };
    }
    if (
      ['ReceivedExpirationDate'].includes(column.field) &&
      row.ReceivedExpirationDate &&
      row.ShelfLifeExpirationDate !== row.ReceivedExpirationDate
    ) {
      return {
        backgroundColor: '#fff9db',
      };
    }
    return null;
  },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'Tally',
      width: 56,
      fixed: 'left',
      title: '理货',
      slots: { default: 'opt' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      fixed: 'left',
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      /* filters: [{ data: null }],
      filterRender: { name: 'FilterInput' }, */
      slots: { default: 'material' },
    },
    {
      field: 'Invoice',
      title: '发票号',
      width: 116,
      editRender: {
        name: 'AInput',
        autofocus: '',
        props: {
          disabled: props.isClosed,
          size: 'small',
          autocomplete: 'off',
          maxlength: 30,
        },
      },
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      /* className: 'm-0 p-0', */
    },
    {
      field: 'Inbound',
      title: 'Inbound',
      width: 88,
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
    },
    {
      field: 'InboundItem',
      title: 'Item',
      width: 72,
      visible: false,
    },
    {
      field: 'InboundBatch',
      title: '产品批号',
      width: 80,
      slots: { default: 'ibdbatch' },
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
    },
    {
      field: 'ManufactureDate',
      title: '制造日期',
      width: 88,
      formatter: 'formatDate',
    },
    {
      field: 'ShelfLifeExpirationDate',
      title: '产品效期',
      width: 88,
      formatter: 'formatDate',
      visible: false,
    },
    {
      field: 'EstimatedReceivedQty',
      title: '产品数量',
      width: 80,
    },
    {
      field: 'ReceivingStatus',
      title: '收货状态',
      width: 88,
      formatter({ cellValue }) {
        return cellValue ? '已收货' : '待收货';
      },
      filters: [
        { label: '待收货', value: false },
        { label: '已收货', value: true },
      ],
      filterMultiple: false,
      slots: { default: 'rStatus' },
    },
    {
      field: 'InboundGRStatus',
      title: 'GR',
      width: 56,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'gStatus' },
    },
    {
      field: 'iIsCareful',
      title: '精细理货',
      width: 88,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'cStatus' },
    },
    {
      field: 'SSCC',
      title: 'SSCC',
      width: 144,
      editRender: {
        name: 'AInput',
        autofocus: '',
        props: { size: 'small', autocomplete: 'off', maxlength: 20 },
      },
    },
    {
      field: 'PalletNo',
      title: '入库托盘号',
      width: 88,
      editRender: {
        name: 'AInput',
        autofocus: '',
        props: {
          disabled: props.isClosed,
          size: 'small',
          autocomplete: 'off',
          maxlength: 20,
        },
      },
    },
    {
      field: 'ReceivedBoxNum',
      title: '收货箱数',
      width: 72,
    },
    {
      field: 'ReceivedQty',
      title: '收货数量',
      width: 80,
    },
    {
      field: 'MissingQty',
      title: '少货',
      width: 56,
    },
    {
      field: 'ReceivedBatch',
      title: '实收批号',
      width: 80,
      slots: { default: 'receivedbatch' },
    },
    {
      field: 'ReceivedExpirationDate',
      title: '实收效期',
      width: 88,
      /* formatter: 'formatDate', */
    },
    {
      field: 'lifespanInDays',
      title: '期限',
      width: 64,
      /* formatter: 'formatDate', */
    },
    {
      field: 'ReceivedBy',
      title: '收货人',
      width: 80,
      /* editRender: {
        name: 'AInput',
        autofocus: '',
        props: { size: 'small', autocomplete: 'off', maxlength: 16 },
      }, */
    },
    {
      field: 'ReceivedDate',
      title: '收货时间',
      width: 164,
      formatter: 'formatDateTime',
      slots: { edit: 'receiveddate_edit' },
      editRender: {
        name: 'ADatePicker',
        /* props: { showTime: true, valueFormat: 'YYYY-MM-DD HH:mm:ss' }, */
      },
    },
    {
      field: 'Remark',
      title: '备注',
      width: 150,
      editRender: {
        name: 'AInput',
        autofocus: '',
        props: {
          disabled: props.isClosed,
          size: 'small',
          autocomplete: 'off',
          maxlength: 128,
        },
      },
    },

    {
      field: 'iCName',
      title: '中文品名',
      width: 240,
    },
    { field: 'iBarCode', title: '条形码', width: 112 },
    { field: 'iMatType', title: '物料类型', width: 80 },
    { field: 'iDivision', title: 'Division', width: 80 },
    {
      field: 'iBrandName',
      title: '品牌',
      width: 80,
    },
    { field: 'iOriginName', title: '原产国', width: 80 },
    { field: 'iSpecifaction', title: '规格', width: 80 },
    { field: 'iPCB', title: 'PCB', width: 72 },
    { field: 'iHsCode', title: 'HsCode', width: 100 },
    { field: 'PCLInWMS', title: '实际层规', width: 72 },
    { field: 'PCPInWMS', title: '实际托规', width: 72 },
    { field: 'MaterialRemark', title: '物料备注', width: 128 },
  ],
  editConfig: {
    trigger: 'dblclick',
    mode: 'row',
    showIcon: true,
    showStatus: true,
    icon: 'fa fa-pencil',
    beforeEditMethod: () => {
      if (props.isClosed) {
        return false;
      }
      return true;
    },
  },
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (
          [
            'EstimatedReceivedQty',
            'MissingQty',
            'ReceivedBoxNum',
            'ReceivedQty',
          ].includes(column.field)
        ) {
          return sumNum(data, column.field);
        }
        if (['ReceivingStatus'].includes(column.field)) {
          const status = [
            ...new Set(
              data
                .map((item) => item.ReceivingStatus)
                .filter((item) => item !== ''),
            ),
          ];
          if (status.length === 1) {
            return `${status[0] ? '已收货' : '待收货'}`;
          } else if (status.length > 1) {
            return `部分入库`;
          }
        }
        if (['PalletNo'].includes(column.field)) {
          const traynos = [
            ...new Set(
              data.map((item) => item.PalletNo).filter((item) => item !== ''),
            ),
          ];
          return `托盘数: ${traynos.length}`;
        }
        if (['iDivision'].includes(column.field)) {
          /* allBrand.value = [
            ...new Set(
              data.map((item) => item.iBrandName).filter((item) => item !== ''),
            ),
          ]; */
          allDivisions.value = [
            ...new Set(
              data.map((item) => item.iDivision).filter((item) => item !== ''),
            ),
          ];
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async ({ $grid }) => {
        const res = await getInOrderItem(props.id);
        allBrands.value = res.allBrands;
        const iBrandColumn = $grid.getColumnByField('iBrandName');
        if (iBrandColumn) {
          $grid.setFilter(iBrandColumn, allBrands.value);
        }
        return res.data;
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuSplit',
            disabled: false,
            name: '预拆分',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  editClosed({ row, rowIndex }) {
    handleUpdateByRow(row, rowIndex);
  },
  menuClick({ menu, row }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      case 'menuSplit': {
        handleSplit(row);
        break;
      }
      default: {
        break;
      }
    }
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function handleSplit(rowId: string) {
  openSplitFormModal(rowId);
}
function handleExport() {
  gridApi.grid.exportData({
    filename: `入库订单明细_${props.hbl}`,
    original: false,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}

function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}
/* function openCreateModal() {
  createModalApi.open();
} */

function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
const [InvoiceForm, invoiceFormApi] = useVbenForm({
  handleSubmit: handleUpdateInvoice,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入发票号',
        autocomplete: 'off',
        maxlength: 30,
      },
      fieldName: 'Invoice',
      label: '发票号',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});
const [InvoiceModal, invoiceModalApi] = useVbenModal({
  onConfirm: async () => {
    await invoiceFormApi.submitForm();
  },
});
async function handleUpdateInvoice(values: Record<string, any>) {
  const check = await invoiceFormApi.validate();
  if (check.valid) {
    const checkedRecords = gridApi.grid
      .getCheckboxRecords()
      .map((item: any) => item.Guid);
    invoiceModalApi.setState({ confirmLoading: true });
    invoiceModalApi.setState({ loading: true });
    bwInOrderItemInvoiceUpdate(props.id, checkedRecords, values.Invoice)
      .then(() => {
        handleSuccess();
        invoiceModalApi.close();
      })
      .catch(() => {})
      .finally(() => {
        invoiceModalApi.setState({ loading: false });
        invoiceModalApi.setState({ confirmLoading: false });
      });
  }
}
function handleUpdateByRow(row: any, rowIndex: number) {
  if (gridApi.grid.isUpdateByRow(row)) {
    gridApi.setLoading(true);
    bwInOrderItemUpdate(row)
      .then(() => {
        message.success(`序号${rowIndex + 1} 保存成功！ `);
        handleSuccess();
      })
      .catch(() => {
        gridApi.grid.revertData(row);
      })
      .finally(() => {
        gridApi.setLoading(false);
      });
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
function operation(typ: string) {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  bwInOrderItemStatusUpdate(props.id, checkedRecords, typ)
    .then(() => {
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}
function handleMenuClick(e: any) {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选产品明细`);
    return;
  }
  switch (e.key) {
    case 'Cancel0': {
      Modal.confirm({
        content: `如果已进行CSAR检查登记，可能需要重新操作`,
        onCancel() {},
        onOk() {
          operation('cc');
        },
        title: `撤销收货 - 清除理货信息`,
        okButtonProps: { danger: true },
      });
      break;
    }
    case 'Cancel1': {
      Modal.confirm({
        content: `如果已进行CSAR检查登记，可能需要重新操作`,
        onCancel() {},
        onOk() {
          operation('cs');
        },
        title: `撤销收货 - 保留理货信息`,
        okButtonProps: { danger: true },
      });
      break;
    }
    case 'InvoiceUpdate': {
      invoiceModalApi.open();
      break;
    }
    case 'ReReceived': {
      operation('re');
      break;
    }
    default: {
      break;
    }
  }
}
function handleOpenStockSetup() {
  if (!props.inStatus) {
    message.info('库存初始化前请先完成进仓确认，确认收货库区');
    return;
  }
  stockSetupModalApi.setData({ id: props.id });
  stockSetupModalApi.open();
}
/* function handleDownload() {
  downloadTemplateFile('StockSetup').then((res) => {
    downloadFileFromBlobPart({
      source: res,
      fileName: `StockSetup.xlsx`,
    });
  });
} */

// 处理表格空白区域点击事件
function handleTableClick(event: Event) {
  // 确保是鼠标点击事件
  if (!(event instanceof MouseEvent)) return;

  const target = event.target as HTMLElement;

  // 检查是否点击在表格的空白区域（表格内但不在数据行上）
  const isInTableBody =
    target.closest('.vxe-table--body-wrapper') ||
    target.classList.contains('vxe-table--body-wrapper');
  const isInDataRow = target.closest('tr.vxe-body--row');

  // 如果点击在表格主体内但不在数据行上，说明是空白区域
  if (isInTableBody && !isInDataRow) {
    // 检查是否有正在编辑的行
    const editRecord = gridApi.grid?.getEditRecord();
    if (editRecord && editRecord.row) {
      // 有正在编辑的行，触发保存
      const editRow = editRecord.row;
      const rowIndex = gridApi.grid?.getRowIndex(editRow);
      gridApi.grid?.clearEdit();
      if (editRow && rowIndex !== undefined) {
        handleUpdateByRow(editRow, rowIndex);
      }
    }
  }
}

onMounted(() => {
  nextTick(() => {
    // 等待表格渲染完成后添加事件监听器
    const gridElement = document.querySelector('.vxe-grid');
    if (gridElement) {
      gridElement.addEventListener('click', handleTableClick);
    }
  });
});

onUnmounted(() => {
  // 清理事件监听器
  const gridElement = document.querySelector('.vxe-grid');
  if (gridElement) {
    gridElement.removeEventListener('click', handleTableClick);
  }
});
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">产品明细</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden md:block">
          <a-button
            :disabled="props.isClosed"
            class="ml-2"
            size="small"
            type="primary"
            @click="openInboundImportModal()"
          >
            Inbound导入
          </a-button>
          <a-button
            :disabled="props.isClosed"
            class="ml-2"
            size="small"
            @click="openInboundRemoveDrawer()"
          >
            Inbound移除
          </a-button>
          <!-- <a-button
            :disabled="props.isClosed"
            class="ml-2"
            danger
            size="small"
            @click="1"
          >
            Inbound缺失SSCC
          </a-button> -->
          <a-button
            :disabled="props.isClosed"
            class="ml-2"
            danger
            type="dashed"
            size="small"
            @click="handleOpenStockSetup()"
          >
            库存初始化
          </a-button>
        </div>
      </template>
      <template #toolbar-tools>
        <Dropdown :disabled="props.isClosed" :trigger="['click']">
          <template #overlay>
            <Menu @click="handleMenuClick">
              <MenuItem key="InvoiceUpdate">更新发票号</MenuItem>
              <MenuItem key="Cancel0">撤销收货/清除理货信息</MenuItem>
              <MenuItem key="Cancel1">撤销收货/保留理货信息</MenuItem>
              <MenuItem key="ReReceived">恢复收货标记</MenuItem>
            </Menu>
          </template>
          <a-button class="mr-1" size="small"> 批量操作 </a-button>
        </Dropdown>
      </template>
      <template #opt="{ row }">
        <a-button
          :disabled="row.ReceivingStatus || !props.inStatus"
          class="!pl-1 !pr-1"
          size="small"
          type="link"
          @click="openReceivingModal(row.Guid)"
        >
          <AntSandBox class="size-6 pt-1" />
        </a-button>
      </template>
      <template #rStatus="{ row }">
        <Tag :color="row.ReceivingStatus ? 'green' : 'red'">
          {{ row.ReceivingStatus ? '已收货' : '待收货' }}
        </Tag>
      </template>
      <template #cStatus="{ row }">
        <Tag :color="row.iIsCareful ? 'green' : 'blue'" class="mr-0">
          {{ row.iIsCareful ? '是' : '否' }}
        </Tag>
      </template>
      <template #gStatus="{ row }">
        <Tag :color="row.InboundGRStatus ? 'green' : 'red'" class="mr-0">
          {{ row.InboundGRStatus ? '是' : '否' }}
        </Tag>
      </template>
      <template #material="{ row }">
        <Popconfirm
          cancel-text="产品主数据"
          ok-text="基础物料"
          title="数据展示"
          @cancel="openSkuDrawer(row.MatCode)"
          @confirm="openMaterialDrawer(row.MatCode)"
        >
          <a class="text-blue-500" @click.prevent>
            {{ row.MatCode }}
          </a>
        </Popconfirm>
      </template>
      <template #ibdbatch="{ row }">
        <a
          v-if="row.InboundBatch"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.InboundBatch)"
        >
          {{ row.InboundBatch }}
        </a>
      </template>
      <template #receivedbatch="{ row }">
        <a
          v-if="row.ReceivedBatch"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.ReceivedBatch)"
        >
          {{ row.ReceivedBatch }}
        </a>
      </template>
      <template #receiveddate_edit="{ row }">
        <DatePicker
          v-model:value="row.ReceivedDate"
          :disabled="row.InboundGRStatus || !row.ReceivingStatus"
          :show-time="true"
          size="small"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </template>
      <template #bottom>
        <div class="h-6 pt-1 text-sm font-bold">
          <div class="float-left hidden md:block">
            <span>{{ '品牌: ' }}</span>
            <Tag
              v-for="(item, index) in allBrands"
              :key="index"
              :value="item.value"
              color="#108ee9"
            >
              {{ item.value }}
            </Tag>
          </div>
          <div class="float-right">
            <span>{{ 'Division: ' }}</span>
            <Tag
              v-for="(item, index) in allDivisions"
              :key="index"
              color="#87d068"
            >
              {{ item }}
            </Tag>
            <a-button size="small" @click="openSplitModal()">
              拆分记录
            </a-button>
          </div>
        </div>
      </template>
    </Grid>
    <InboundImportModal @success="handleSuccess" />
    <InboundRemoveDrawer @success="handleSuccess" />
    <ReceivingModal @success="handleSuccess" />
    <SplitModal @success="handleSuccess" />
    <MaterialDrawer @success="handleSuccess" />
    <SkuDrawer />
    <BatchDrawer />
    <InvoiceModal :fullscreen-button="false" title="批量更新">
      <InvoiceForm />
    </InvoiceModal>
    <StockSetupModal @success="handleSuccess" />
    <SplitFormModal @success="handleSuccess" />
  </div>
</template>
